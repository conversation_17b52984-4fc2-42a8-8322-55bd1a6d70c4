# MLX90393 Triaxis® 磁传感器技术文档

The Wake-Up on Change (WOC) functionality can be set by the master with as main purpose to only receive
an interrupt when a certain threshold is crossed. The WOC mode will always compare a new burst value with
a reference value to assess if the difference between both exceeds a user-defined threshold. The reference
value is defined as one of the following:
 The first measurement of WOC mode is stored as reference value once. This measurement at “t=0” is
then the basis for comparison or,
 The reference for acquisition(t) is always acquisition(t-1), in such a way that the INT signal will only
be set if the derivative of any component exceeds a threshold.
The in-application programmability is the same as for burst mode, but now the thresholds for setting the
interrupt are also programmable by the user, as well as the reference, if the latter is data(t=0) or data(t-1)


16.2.6. TRIG_INT_SEL
When set to 0 the TRIG_INT pin is in trigger mode. When set to 1 the TRIG_INT pin acts as an interrupt pin


## 1. 产品概述

MLX90393是一款高性能的三轴磁场传感器，采用Melexis专有的Triaxis®技术，支持X、Y、Z三轴磁场测量以及温度测量。

### 1.1 主要特性
- **测量范围**: 5-50mT (宽动态范围)
- **供电电压**: 2.2V-3.6V (模拟)，1.65V-VDD (数字IO)
- **分辨率**: 最高16位输出
- **通信接口**: I²C和SPI
- **工作温度**: -20°C至85°C (S版本)，-40°C至85°C (E版本)
- **封装**: QFN-16 (3×3×1mm) 或 UTDFN-8 (2.5×2mm)

## 2. I²C通信配置

### 2.1 I²C地址配置
根据A1和A0引脚的连接配置7位I²C地址：

| A1 | A0 | I²C地址 (7位) |
|----|----| -------------|
| 0  | 0  | 0x0C         |
| 0  | 1  | 0x0D         |
| 1  | 0  | 0x0E         |
| 1  | 1  | 0x0F         |

### 2.2 命令格式
所有命令都是8位，格式为：`命令码[7:4] + 参数[3:0]`

| 命令名称 | 命令码 | 描述 |
|---------|-------|------|
| SB | 0x1_ | 启动突发模式 |
| SW | 0x2_ | 启动唤醒变化模式 |
| SM | 0x3_ | 启动单次测量模式 |
| RM | 0x4_ | 读取测量数据 |
| RR | 0x5_ | 读取寄存器 |
| WR | 0x6_ | 写入寄存器 |
| EX | 0x80 | 退出当前模式 |
| HR | 0xD0 | 内存召回 |
| HS | 0xE0 | 内存存储 |
| RT | 0xF0 | 复位 |

### 2.3 轴选择参数 (zyxt)
命令参数的低4位用于选择测量轴：

| 位 | 功能 |
|----|------|
| bit3 | Z轴使能 |
| bit2 | Y轴使能 |
| bit1 | X轴使能 |
| bit0 | 温度使能 |

**示例**: 0x3F = 测量X、Y、Z轴和温度

## 3. 寄存器映射

### 3.1 客户区域寄存器 (0x00-0x1F)

#### 寄存器 0x00
| 位段 | 名称 | 描述 |
|------|------|------|
| [15:14] | ANA_RESERVED_LOW | 保留位，勿修改 |
| [13] | BIST | 内置自测试线圈使能 |
| [12] | Z_SERIES | Z轴串联模式使能 |
| [11:5] | GAIN_SEL[2:0] | 模拟增益选择 |
| [4:0] | HALLCONF[3:0] | 霍尔板配置 |

#### 寄存器 0x01
| 位段 | 名称 | 描述 |
|------|------|------|
| [15] | TRIG_INT_SEL | 0=触发模式, 1=中断模式 |
| [14:13] | COMM_MODE[1:0] | 通信模式选择 |
| [12] | WOC_DIFF | 唤醒变化模式配置 |
| [11] | EXT_TRIG | 外部触发使能 |
| [10] | TCMP_EN | 温度补偿使能 |
| [9:6] | BURST_SEL[3:0] | 突发模式轴选择 |
| [5:0] | BURST_DATA_RATE[5:0] | 突发数据率 |

#### 寄存器 0x02
| 位段 | 名称 | 描述 |
|------|------|------|
| [15:14] | OSR2[1:0] | 温度ADC过采样率 |
| [13:12] | RES_Z[1:0] | Z轴分辨率 |
| [11:10] | RES_Y[1:0] | Y轴分辨率 |
| [9:8] | RES_X[1:0] | X轴分辨率 |
| [7:5] | DIG_FILT[2:0] | 数字滤波器选择 |
| [1:0] | OSR[1:0] | 磁场ADC过采样率 |

### 3.2 关键配置参数

#### GAIN_SEL[2:0] - 增益选择
| 值 | 增益 | X/Y轴灵敏度(μT/LSB) | Z轴灵敏度(μT/LSB) |
|----|------|-------------------|------------------|
| 0 | 5×    | 0.751 | 1.210 |
| 1 | 4×    | 0.601 | 0.968 |
| 2 | 3×    | 0.451 | 0.726 |
| 3 | 2.5×  | 0.376 | 0.605 |
| 4 | 2×    | 0.300 | 0.484 |
| 5 | 1.67× | 0.250 | 0.403 |
| 6 | 1.33× | 0.200 | 0.323 |
| 7 | 1×    | **0.150** | **0.242** |

*注：表中数值基于HALLCONF=0xC, RES=0, 温度25°C*

#### RES_XYZ[1:0] - 分辨率选择
| 值 | 分辨率 | 输出范围 |
|----|--------|----------|
| 0 | 16位 | ±32768 LSB |
| 1 | 15位 | ±16384 LSB |
| 2 | 14位 | ±8192 LSB |
| 3 | 13位 | ±4096 LSB |

#### OSR[1:0] - 过采样率
| 值 | 过采样率 | 噪声改善 |
|----|----------|----------|
| 0 | 1× | 基准 |
| 1 | 2× | √2倍改善 |
| 2 | 4× | 2倍改善 |
| 3 | 8× | 2√2倍改善 |

#### DIG_FILT[2:0] - 数字滤波器
| 值 | 滤波级别 | 转换时间影响 |
|----|----------|--------------|
| 0 | 无滤波 | 最快 |
| 1-3 | 轻度滤波 | 中等 |
| 4-7 | 重度滤波 | 最慢 |

## 4. 工作模式

### 4.1 单次测量模式 (SM)
```
发送: SM命令 + 轴选择
等待: 测量完成 (50-100ms)
读取: RM命令 + 轴选择
```

### 4.2 突发模式 (SB)
```
发送: SB命令 + 轴选择
循环: 定期读取RM命令
退出: EX命令
```

### 4.3 唤醒变化模式 (SW)
```
发送: SW命令 + 轴选择
等待: 磁场变化触发中断
读取: RM命令获取数据
```

## 5. 数据格式与计算

### 5.1 状态字节解析
| 位 | 名称 | 描述 |
|----|------|------|
| 7 | BURST_MODE | 突发模式标志 |
| 6 | WOC_MODE | 唤醒变化模式标志 |
| 5 | SM_MODE | 单次测量模式标志 |
| 4 | ERROR | 错误标志 |
| 3 | SED | 单错误检测 |
| 2 | RS | 复位标志 |
| 1:0 | D[1:0] | 数据字节数 = 2×D[1:0]+2 |

### 5.2 原始数据读取顺序
数据按照 **T-X-Y-Z** 顺序返回（与命令参数zyxt相反）

### 5.3 磁场物理值计算公式

#### 基本转换公式
```
磁场(μT) = ADC值 × 灵敏度(μT/LSB)
```

#### 考虑增益和分辨率的完整公式
```
实际灵敏度 = 基准灵敏度 × (2^RES_设置)
```

**示例计算**:
- GAIN_SEL = 7, RES_X = 0: X轴灵敏度 = 0.150 μT/LSB
- GAIN_SEL = 7, RES_X = 1: X轴灵敏度 = 0.150 × 2 = 0.300 μT/LSB

### 5.4 温度计算公式
```
温度(°C) = (ADC值 - 46244) ÷ 45.2
```

### 5.5 磁场矢量计算
```
磁场强度 = √(Bx² + By² + Bz²)
```

## 6. 典型配置示例

### 6.1 高精度配置
```c
// 寄存器0x00: 增益1×, HALLCONF=0xC
reg0 = (0x7 << 5) | (0xC << 0);  // 0x00EC

// 寄存器0x01: 中断模式
reg1 = (0x1 << 15);  // 0x8000

// 寄存器0x02: 16位分辨率, OSR=8×, 滤波器4
reg2 = (0x0 << 11) | (0x0 << 9) | (0x0 << 7) | (0x4 << 2) | (0x3 << 0);  // 0x0013
```

### 6.2 快速响应配置
```c
// 寄存器0x00: 增益5×, HALLCONF=0x0
reg0 = (0x0 << 5) | (0x0 << 0);  // 0x0000

// 寄存器0x02: 13位分辨率, OSR=1×, 无滤波
reg2 = (0x3 << 11) | (0x3 << 9) | (0x3 << 7) | (0x0 << 2) | (0x0 << 0);  // 0x1B00
```

## 7. 时序要求

### 7.1 转换时间计算
```
T_CONV_MAG = 67 + 64 × 2^OSR × (2 + 2^DIG_FILT) μs
T_CONV_TEMP = 67 + 192 × 2^OSR2 μs
```

### 7.2 模式切换时序
- 待机到激活: 360μs (典型)
- 空闲到待机: 220μs (典型)
- 转换结束: 100μs (典型)

## 8. 应用注意事项

### 8.1 电源要求
- VDD: 2.2V-3.6V (模拟供电)
- VDD_IO: 1.65V-VDD (数字IO供电)
- 非易失性存储器写入需要VDD ≥ 3.3V

### 8.2 磁场测量范围
- 输出饱和: ±50mT
- 推荐工作范围: ±4.8mT (最高增益时)

### 8.3 温度影响
- 偏移温漂: < ±1000 LSB (全温度范围)
- 灵敏度温漂: ±3% (相对25°C)

### 8.4 交叉轴灵敏度
- X/Y轴间: < ±1%
- X/Y对Z轴: < ±1%
- Z轴对X/Y轴: < ±1%

## 9. 调试建议

### 9.1 初始化检查清单
1. ✅ 确认I²C地址正确
2. ✅ 验证寄存器写入成功
3. ✅ 检查状态字节无错误
4. ✅ 测试单次测量功能

### 9.2 常见问题排查
| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 无I²C响应 | 地址错误/接线问题 | 检查A0/A1连接 |
| 数据不更新 | 测量未启动 | 发送SM/SB命令 |
| 数值异常 | 增益/分辨率设置错误 | 检查寄存器配置 |
| 噪声过大 | 滤波不足 | 增加DIG_FILT值 |

### 9.3 性能优化建议
- **高精度**: GAIN_SEL=7, RES=0, OSR=3, DIG_FILT=4-7
- **快速响应**: GAIN_SEL=0-3, RES=2-3, OSR=0-1, DIG_FILT=0-2
- **低功耗**: 使用突发模式，调整BURST_DATA_RATE

---

*本文档基于MLX90393数据手册 Rev.007 (2021年1月14日) 编写*
