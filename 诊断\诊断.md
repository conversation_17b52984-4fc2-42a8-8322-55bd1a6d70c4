看了你的代码和寄存器文档，我发现了几个问题。让我帮你分析一下：

## 1. X轴数据异常的原因

### 问题1：轴选择位定义错误
在你的头文件中，轴选择位定义不正确：
```c
// 当前错误的定义
#define MLX90393_AXIS_X             0x02    // 错误！
#define MLX90393_AXIS_Y             0x04    // 错误！
#define MLX90393_AXIS_Z             0x06    // 错误！
```

根据寄存器文档，轴选择位应该是：
```c
// 正确的定义（zyxt格式）
#define MLX90393_AXIS_X             0x08    // bit3 = X轴
#define MLX90393_AXIS_Y             0x04    // bit2 = Y轴
#define MLX90393_AXIS_Z             0x02    // bit1 = Z轴
#define MLX90393_AXIS_T             0x01    // bit0 = 温度
```

### 问题2：数据解析顺序错误
文档明确说明数据返回顺序是 **T-X-Y-Z**，但你的代码按 **X-Y-Z** 顺序解析：

```c
// 当前错误的解析顺序
raw_x = (rx_data[1] << 8) | rx_data[2];  // 实际是温度数据！
raw_y = (rx_data[3] << 8) | rx_data[4];  // 实际是X轴数据
raw_z = (rx_data[5] << 8) | rx_data[6];  // 实际是Y轴数据
```

## 2. 寄存器配置问题

### 问题3：寄存器位域定义错误
```c
// 当前错误的寄存器2配置
new_reg2 |= (MLX90393_RES_Z & 0x3) << 11;   // 错误位置
new_reg2 |= (MLX90393_RES_Y & 0x3) << 9;    // 错误位置
new_reg2 |= (MLX90393_RES_X & 0x3) << 7;    // 错误位置
```

根据寄存器文档，正确的位域应该是：
```c
// 正确的寄存器2配置
new_reg2 |= (MLX90393_RES_Z & 0x3) << 12;   // bit[13:12]
new_reg2 |= (MLX90393_RES_Y & 0x3) << 10;   // bit[11:10]
new_reg2 |= (MLX90393_RES_X & 0x3) << 8;    // bit[9:8]
```

## 修复方案## 立即修复步骤

1. **更新轴选择位定义**：在`mlx90393.h`中替换轴选择宏定义

2. **修正寄存器配置**：在`MLX90393_Init`函数中更新寄存器2的位域配置

3. **添加调试函数**：使用上面的`MLX90393_DebugRawData`函数来验证数据读取

4. **检查数据解析**：确认数据返回顺序是否正确

## 验证方法

1. 先运行调试函数，查看不同轴选择下的原始数据
2. 观察单轴测试的结果，确认每个轴的数据是否正确
3. 检查寄存器配置是否写入成功

根据你的症状（X轴有大的初始值和正负跳跃），很可能是因为：
- 读取了错误的数据（可能是温度数据被当作X轴数据）
- 轴选择位配置错误导致测量了错误的轴
- 寄存器配置错误影响了数据格式

请按照修复方案更新代码，然后测试看看X轴数据是否正常。



/* ============= 修复方案 1：更新头文件定义 ============= */

// 在 mlx90393.h 中，替换以下定义：

/* 正确的测量轴选择位 - 根据寄存器文档zyxt格式 */
#define MLX90393_AXIS_T             0x01    // bit0 = 温度
#define MLX90393_AXIS_Z             0x02    // bit1 = Z轴
#define MLX90393_AXIS_Y             0x04    // bit2 = Y轴
#define MLX90393_AXIS_X             0x08    // bit3 = X轴
#define MLX90393_AXIS_ALL           (MLX90393_AXIS_X | MLX90393_AXIS_Y | MLX90393_AXIS_Z | MLX90393_AXIS_T)

/* ============= 修复方案 2：更新寄存器配置函数 ============= */

// 在 MLX90393_Init() 函数中，替换寄存器2配置部分：
uint8_t MLX90393_Init_Fixed(MLX90393_Handle_t *mlx, I2C_HandleTypeDef *hi2c)
{
    // ... 其他初始化代码保持不变 ...

    // 配置寄存器2 (分辨率和过采样) - 修正位域定义
    uint16_t reg2_val;
    if (MLX90393_ReadRegister(mlx, 0x02, &reg2_val) == 0) {
        // 构建新的REG2值 - 使用正确的位域
        uint16_t new_reg2 = 0;
        new_reg2 |= (MLX90393_RES_Z & 0x3) << 12;   // RES_Z[1:0] -> bit[13:12]
        new_reg2 |= (MLX90393_RES_Y & 0x3) << 10;   // RES_Y[1:0] -> bit[11:10]
        new_reg2 |= (MLX90393_RES_X & 0x3) << 8;    // RES_X[1:0] -> bit[9:8]
        new_reg2 |= (MLX90393_DIG_FILT & 0x7) << 5; // DIG_FILT[2:0] -> bit[7:5]
        new_reg2 |= (MLX90393_OSR & 0x3);           // OSR[1:0] -> bit[1:0]

        if (MLX90393_WriteRegister(mlx, 0x02, new_reg2) == 0) {
            mlx->res_x = MLX90393_RES_X;
            mlx->res_y = MLX90393_RES_Y;
            mlx->res_z = MLX90393_RES_Z;
            mlx->osr = MLX90393_OSR;
            printf("REG2 updated successfully: 0x%04X\r\n", new_reg2);
        } else {
            printf("Failed to update REG2\r\n");
        }
    }

    // ... 其他初始化代码保持不变 ...
}

/* ============= 修复方案 3：更新数据读取函数 ============= */

uint8_t MLX90393_ReadSingleMeasurement_Fixed(MLX90393_Handle_t *mlx, MLX90393_Data_t *data)
{
    HAL_StatusTypeDef hal_result;
    uint8_t tx_data[2];
    uint8_t rx_data[9];  // 状态字节 + 8字节数据 (TXYZ各2字节)
    uint16_t raw_t, raw_x, raw_y, raw_z;

    // 步骤1：发送单次测量命令 - 使用正确的轴选择位
    uint8_t axes = MLX90393_AXIS_X | MLX90393_AXIS_Y | MLX90393_AXIS_Z;  // 0x0E
    tx_data[0] = 0x30 | axes;  // MLX90393_CMD_SM | axes

    hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) return 1;

    // 接收状态字节
    hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) return 1;

    // 检查错误
    if (rx_data[0] & 0x10) {  // ERROR_BIT
        return 1;
    }

    // 等待测量完成
    HAL_Delay(10);

    // 步骤2：读取测量结果
    tx_data[0] = 0x40 | axes;  // MLX90393_CMD_RM | axes

    hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 1, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) return 1;

    // 读取数据：状态字节 + XYZ数据（按照T-X-Y-Z顺序返回，但我们没选择T）
    // 由于我们选择了XYZ（没有T），返回顺序是：X-Y-Z
    hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, 7, MLX90393_TIMEOUT);
    if (hal_result != HAL_OK) return 1;

    // 检查读取状态
    if (rx_data[0] & 0x10) {  // ERROR_BIT
        printf("RM command error: 0x%02X\r\n", rx_data[0]);
        return 1;
    }

    // 正确解析数据：由于没有选择温度，数据顺序就是X-Y-Z
    raw_x = (rx_data[1] << 8) | rx_data[2];  // X轴数据
    raw_y = (rx_data[3] << 8) | rx_data[4];  // Y轴数据
    raw_z = (rx_data[5] << 8) | rx_data[6];  // Z轴数据

    printf("Raw data: X=0x%04X Y=0x%04X Z=0x%04X\r\n", raw_x, raw_y, raw_z);

    // 应用位移修正
    uint16_t corrected_x = raw_x >> MLX90393_X_SHIFT;
    uint16_t corrected_y = raw_y >> MLX90393_Y_SHIFT;
    uint16_t corrected_z = raw_z >> MLX90393_Z_SHIFT;

    // 转换为物理值
    data->x = MLX90393_ConvertMagneticField(corrected_x, mlx->gain_sel, mlx->res_x, 0);
    data->y = MLX90393_ConvertMagneticField(corrected_y, mlx->gain_sel, mlx->res_y, 0);
    data->z = MLX90393_ConvertMagneticField(corrected_z, mlx->gain_sel, mlx->res_z, 1);
    data->temp = 0.0f;

    // 应用偏移补偿
    #if MLX90393_X_OFFSET_ENABLE
    data->x -= MLX90393_X_OFFSET_VALUE;
    #endif

    data->x -= offset_data.x;
    data->y -= offset_data.y;
    data->z -= offset_data.z;

    return 0;
}

/* ============= 修复方案 4：增强的诊断函数 ============= */

void MLX90393_DebugRawData(MLX90393_Handle_t *mlx)
{
    HAL_StatusTypeDef hal_result;
    uint8_t tx_data[2];
    uint8_t rx_data[9];

    printf("\r\n=== Raw Data Debug ===\r\n");

    // 测试不同的轴选择组合
    uint8_t test_cases[] = {
        MLX90393_AXIS_X,                    // 只测X轴
        MLX90393_AXIS_Y,                    // 只测Y轴
        MLX90393_AXIS_Z,                    // 只测Z轴
        MLX90393_AXIS_X | MLX90393_AXIS_Y,  // 测XY轴
        MLX90393_AXIS_X | MLX90393_AXIS_Y | MLX90393_AXIS_Z  // 测XYZ轴
    };

    const char* test_names[] = {
        "X only", "Y only", "Z only", "XY", "XYZ"
    };

    for (int i = 0; i < 5; i++) {
        uint8_t axes = test_cases[i];
        printf("\n--- Test: %s (axes=0x%02X) ---\r\n", test_names[i], axes);

        // 发送SM命令
        tx_data[0] = 0x30 | axes;
        hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 1, MLX90393_TIMEOUT);
        if (hal_result != HAL_OK) {
            printf("SM command failed\r\n");
            continue;
        }

        // 读取状态
        hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, 1, MLX90393_TIMEOUT);
        if (hal_result != HAL_OK) {
            printf("Status read failed\r\n");
            continue;
        }
        printf("SM Status: 0x%02X\r\n", rx_data[0]);

        HAL_Delay(50);  // 等待转换完成

        // 发送RM命令
        tx_data[0] = 0x40 | axes;
        hal_result = HAL_I2C_Master_Transmit(mlx->hi2c, mlx->address, tx_data, 1, MLX90393_TIMEOUT);
        if (hal_result != HAL_OK) {
            printf("RM command failed\r\n");
            continue;
        }

        // 计算预期数据长度
        int data_bytes = 0;
        if (axes & MLX90393_AXIS_T) data_bytes += 2;
        if (axes & MLX90393_AXIS_X) data_bytes += 2;
        if (axes & MLX90393_AXIS_Y) data_bytes += 2;
        if (axes & MLX90393_AXIS_Z) data_bytes += 2;

        // 读取数据
        hal_result = HAL_I2C_Master_Receive(mlx->hi2c, mlx->address, rx_data, data_bytes + 1, MLX90393_TIMEOUT);
        if (hal_result != HAL_OK) {
            printf("Data read failed\r\n");
            continue;
        }

        printf("RM Status: 0x%02X\r\n", rx_data[0]);
        printf("Raw bytes (%d): ", data_bytes);
        for (int j = 1; j <= data_bytes; j++) {
            printf("0x%02X ", rx_data[j]);
        }
        printf("\r\n");

        HAL_Delay(100);
    }

    printf("=== Debug Complete ===\r\n\r\n");
}
